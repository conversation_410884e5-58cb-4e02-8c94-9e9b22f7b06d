from sklearn.metrics import classification_report
import pandas as pd

# y_true ve y_pred_classes daha önce elde edilmiş olmalı
# class_names → val_gen.class_indices'den geliyor

report = classification_report(
    y_true,
    y_pred_classes,
    target_names=class_names,
    output_dict=True  # Sözlük formatında al
)

# Pandas DataFrame'e dönüştür
df_report = pd.DataFrame(report).transpose()

# İlk 114 satır sadece sınıf metriklerini içerir (macro avg, accuracy vs sonradan gelir)
df_classes = df_report.iloc[:len(class_names)]

# <PERSON><PERSON><PERSON><PERSON>lar<PERSON> g<PERSON> (eğer Jupyter/Notebook kullanıyorsan)
print(df_classes)

# CSV olarak kaydetmek istersen:
df_classes.to_csv("sinif_performans_raporu.txt", encoding="utf-8")
