import os
import tensorflow as tf
from tensorflow.keras.layers import (GlobalAveragePooling2D, Dense, Multiply, Reshape,
                                     Conv2D, UpSampling2D, LayerNormalization)
from tensorflow.keras.models import Model
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.optimizers import Adam
import pandas as pd

IMAGE_SIZE = (128, 128)
BATCH_SIZE = 8
EPOCHS = 30
LR = 1e-4
NUM_SEG_CLASSES = 116


img_dir  = "plantsegv3/images"
mask_dir = "plantsegv3/annotations"
metadata_path  = "plantsegv3/Metadatav2.csv"


from tensorflow.keras.preprocessing.image import ImageDataGenerator
tmp_gen = ImageDataGenerator(rescale=1./255).flow_from_directory(
    "plantsegv3/classified/train",
    target_size=IMAGE_SIZE,
    batch_size=8,
    class_mode="categorical"
)
class_indices = tmp_gen.class_indices
NUM_CLASSES = len(class_indices)
print("📌 Sınıf sayısı:", NUM_CLASSES)


def simple_attention_block(inputs):
    x = GlobalAveragePooling2D()(inputs)
    x = Dense(inputs.shape[-1] // 8, activation='relu')(x)
    x = Dense(inputs.shape[-1], activation='sigmoid')(x)
    x = Reshape((1, 1, inputs.shape[-1]))(x)
    return Multiply()([inputs, x])


def parse_image(img_path, mask_path, label, image_size=IMAGE_SIZE):
    img = tf.io.read_file(img_path)
    img = tf.image.decode_jpeg(img, channels=3)
    img = tf.image.resize(img, image_size)
    img = tf.cast(img, tf.float32) / 255.0

    mask = tf.io.read_file(mask_path)
    mask = tf.image.decode_png(mask, channels=1)
    mask = tf.image.resize(mask, image_size, method="nearest")
    mask = tf.cast(mask, tf.uint8)
    mask = tf.one_hot(tf.squeeze(mask), NUM_SEG_CLASSES)

    return img, {"classification": tf.one_hot(label, NUM_CLASSES),
                 "segmentation": mask}

def create_dataset(metadata, split, image_size=IMAGE_SIZE):
    subset = metadata[metadata['Split'] == split]
    split_folder = split.lower() 
    if split_folder == "training":
        split_folder = "train"
    elif split_folder == "validation":
        split_folder = "val"

    img_paths = [os.path.join(img_dir, split_folder, row['Name']) for _, row in subset.iterrows()]
    mask_paths = [os.path.join(mask_dir, split_folder, row['Label file']) for _, row in subset.iterrows()]
    labels = [class_indices[row['Disease']] for _, row in subset.iterrows()]

    dataset = tf.data.Dataset.from_tensor_slices((img_paths, mask_paths, labels))
    dataset = dataset.map(lambda x, y, z: parse_image(x, y, z, image_size),
                          num_parallel_calls=tf.data.AUTOTUNE)
    dataset = dataset.shuffle(500).batch(BATCH_SIZE).prefetch(tf.data.AUTOTUNE)
    return dataset


metadata = pd.read_csv(metadata_path)


train_dataset = create_dataset(metadata, 'Training')
val_dataset   = create_dataset(metadata, 'Validation')


mobilenet = MobileNetV2(input_shape=IMAGE_SIZE + (3,), include_top=False, weights='imagenet')
mobilenet.trainable = True
fine_tune_at = 100
for layer in mobilenet.layers[:fine_tune_at]:
    layer.trainable = False

features = mobilenet.output
features = simple_attention_block(features)

# Classification 
cls_gap = GlobalAveragePooling2D()(features)
cls_dense = Dense(256, activation='relu')(cls_gap)
cls_dense = LayerNormalization()(cls_dense)
cls_dense = Dense(128, activation='relu')(cls_dense)
cls_dense = LayerNormalization()(cls_dense)
cls_output = Dense(NUM_CLASSES, activation='softmax', name="classification")(cls_dense)

# Segmentation
seg = Conv2D(128, (3,3), activation='relu', padding='same')(features)
seg = UpSampling2D((2,2))(seg)  
seg = Conv2D(64, (3,3), activation='relu', padding='same')(seg)
seg = UpSampling2D((2,2))(seg) 
seg = Conv2D(32, (3,3), activation='relu', padding='same')(seg)
seg = UpSampling2D((2,2))(seg)  
seg = Conv2D(16, (3,3), activation='relu', padding='same')(seg)
seg = UpSampling2D((2,2))(seg)  
seg = Conv2D(8, (3,3), activation='relu', padding='same')(seg)
seg = UpSampling2D((2,2))(seg)  
seg_output = Conv2D(NUM_SEG_CLASSES, (1,1), activation='softmax', name="segmentation")(seg)

model = Model(inputs=mobilenet.input, outputs=[cls_output, seg_output])
model.compile(
    optimizer=Adam(learning_rate=LR),
    loss={"classification": "categorical_crossentropy",
          "segmentation": "categorical_crossentropy"},
    metrics={"classification": "accuracy",
             "segmentation": "accuracy"}
)

model.summary()


history = model.fit(
    train_dataset,
    validation_data=val_dataset,
    epochs=EPOCHS
)

model.save("plantseg_cls_seg.keras")
print("Model kaydedildi")
