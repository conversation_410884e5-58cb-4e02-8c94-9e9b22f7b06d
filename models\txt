import os

train_dir = "C:/Users/<USER>/OneDrive/Masaüstü/plant/Plant_Data/train"
output_file = "C:/Users/<USER>/OneDrive/Masaüstü/plant/classes.txt"

# Alt klasörleri bulunduğu sırayla al
classes = [d for d in os.listdir(train_dir) if os.path.isdir(os.path.join(train_dir, d))]

# Dosyaya yazdır
with open(output_file, "w", encoding="utf-8") as f:
    for cls in classes:
        f.write(cls + "\n")

print(f"✅ {len(classes)} sınıf bulundu ve {output_file} dosyasına kaydedildi.")

