import tensorflow as tf
from tensorflow.keras import layers, models

# Veri yolları
train_path = "Plant Leaf.dataset\train"
val_path = "Plant Leaf.dataset\valid"

# Dataset'i yükle
train_ds = tf.keras.preprocessing.image_dataset_from_directory(
    train_path, image_size=(224, 224), batch_size=32)
val_ds = tf.keras.preprocessing.image_dataset_from_directory(
    val_path, image_size=(224, 224), batch_size=32)

# Sınıf sayısı
num_classes = len(train_ds.class_names)

# Modeli oluştur
model = models.Sequential([
    layers.Conv2D(32, (3,3), activation='relu', input_shape=(224,224,3)),
    layers.MaxPooling2D(2,2),
    
    layers.Conv2D(64, (3,3), activation='relu'),
    layers.MaxPooling2D(2,2),

    layers.Flatten(),
    layers.Dense(128, activation='relu'),
    layers.Dropout(0.5),
    layers.Dense(num_classes, activation='softmax')
])

# Derle ve eğit
model.compile(optimizer='adam',
              loss='sparse_categorical_crossentropy',
              metrics=['accuracy'])

model.fit(train_ds, validation_data=val_ds, epochs=10)