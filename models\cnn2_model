import os
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, BatchNormalization, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import ModelCheckpoint
from tensorflow.keras.regularizers import l2

IMAGE_SIZE = (224, 224)
BATCH_SIZE = 32
EPOCHS = 50
LR = 1e-4

train_dir = 'C:/Users/<USER>/OneDrive/Masaüstü/plant/results/images/train'
val_dir = 'C:/Users/<USER>/OneDrive/Masaüstü/plant/results/images/val'



train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=30,
    zoom_range=0.3,
    width_shift_range=0.2,
    height_shift_range=0.2,
    brightness_range=[0.7, 1.3],
    horizontal_flip=True
)

val_datagen = ImageDataGenerator(rescale=1./255)

train_generator = train_datagen.flow_from_directory(
    train_dir,
    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode='categorical'
)

val_generator = val_datagen.flow_from_directory(
    val_dir,
    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode='categorical'
)

num_classes = train_generator.num_classes

model = Sequential([
    Conv2D(32, (3, 3), activation='relu',kernel_regularizer=l2(1e-4), input_shape=IMAGE_SIZE + (3,)),
    BatchNormalization(),
    MaxPooling2D(pool_size=(2, 2)),

    Conv2D(64, (3, 3), activation='relu',kernel_regularizer=l2(1e-4)),
    BatchNormalization(),
    MaxPooling2D(pool_size=(2, 2)),

    Conv2D(128, (3, 3), activation='relu',kernel_regularizer=l2(1e-4)),
    BatchNormalization(),
    MaxPooling2D(pool_size=(2, 2)),

    Conv2D(256, (3, 3), activation='relu',kernel_regularizer=l2(1e-4)),
    BatchNormalization(),
    MaxPooling2D(pool_size=(2, 2)),

    Flatten(),
    Dense(256, activation='relu',kernel_regularizer=l2(1e-4)),
    Dropout(0.7),
    Dense(num_classes, activation='softmax')
])

model.compile(optimizer=Adam(learning_rate=LR),
              loss='categorical_crossentropy',
              metrics=['accuracy'])

model.summary()


checkpoint = ModelCheckpoint('bitki_cnn_model3_best.keras', monitor='val_accuracy', save_best_only=True)


history = model.fit(
    train_generator,
    validation_data=val_generator,
    epochs=EPOCHS,
    batch_size=BATCH_SIZE,
    callbacks=[checkpoint]
)

# Eğitim grafiği
plt.plot(history.history['accuracy'], label='Eğitim Doğruluğu')
plt.plot(history.history['val_accuracy'], label='Doğrulama Doğruluğu')
plt.title('Model Doğruluk Grafiği')
plt.xlabel('Epoch')
plt.ylabel('Doğruluk')
plt.legend()
plt.show()

class_names = list(val_generator.class_indices.keys())

# Tahminler
Y_true = []
Y_pred = []

steps = val_generator.samples // val_generator.batch_size + 1

for i in range(steps):
    x_batch, y_batch = val_generator[i]
    y_true_batch = np.argmax(y_batch, axis=1)
    y_pred_batch = np.argmax(model.predict(x_batch, verbose=0), axis=1)


    Y_true.extend(y_true_batch)
    Y_pred.extend(y_pred_batch)

Y_true = np.array(Y_true)
Y_pred = np.array(Y_pred)

# Confusion matrix
cm = confusion_matrix(Y_true, Y_pred)
plt.figure(figsize=(10, 8))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=class_names, yticklabels=class_names)
plt.xlabel('Tahmin')
plt.ylabel('Gerçek')
plt.title('Confusion Matrix')
plt.show()

# Detaylı sınıf raporu
print(classification_report(Y_true, Y_pred, target_names=class_names))

#best_model = tf.keras.models.load_model('bitki_cnn_model2_best.keras')

#converter = tf.lite.TFLiteConverter.from_keras_model(best_model)
#converter.optimizations = [tf.lite.Optimize.DEFAULT]

#tflite_model = converter.convert()

#with open('bitki_cnn_model2.tflite', 'wb') as f:
#    f.write(tflite_model)

