import gradio as gr
import tensorflow as tf
from tensorflow.keras.preprocessing import image
import numpy as np
import json


model = tf.keras.models.load_model("C:/Users/<USER>/OneDrive/Masaüstü/plant/bitki_tanima2_best.keras")



with open("C:/Users/<USER>/OneDrive/Masaüstü/plant/models/labels2.txt", "r") as f:
    lines = f.read().splitlines()
    class_indices = {label: idx for idx, label in enumerate(lines)}



class_names = [k for k, v in sorted(class_indices.items(), key=lambda item: item[1])]


def predict(img):
    img = img.resize((128, 128))
    img_array = image.img_to_array(img) / 255.0
    img_array = np.expand_dims(img_array, axis=0)

    predictions = model.predict(img_array)
    class_index = np.argmax(predictions)
    confidence = np.max(predictions)

    predicted_label = class_names[class_index]
    return f"{predicted_label} ({confidence * 100:.2f}%)"

interface = gr.Interface(
    fn=predict,
    inputs=gr.Image(type="pil"),
    outputs="text",
    title="Bitki Tanıma Sistemi",

)

interface.launch()