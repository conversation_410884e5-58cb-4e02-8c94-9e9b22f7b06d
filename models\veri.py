import os
import pandas as pd
import shutil
from tqdm import tqdm

# 📍 Klasör yolları
base_path = "C:/Users/<USER>/OneDrive/Masaüstü/plant/plantsegv3"
image_folder = os.path.join(base_path, "images")
output_base = os.path.join(base_path, "classified")
metadata_file = os.path.join(base_path, "Metadatav2.csv")

print("📁 base_path:", base_path)
print("📁 image_folder:", image_folder)
print("📁 output_base (classified klasörü):", output_base)

# Metadata dosyasını oku
print("\n📊 Metadata dosyası okunuyor...")
df = pd.read_csv(metadata_file)

print(f"📈 Toplam {len(df)} görüntü bulundu")
print(f"📈 Sınıflar: {df['Disease'].unique()}")
print(f"📈 Split dağılımı:")
print(df['Split'].value_counts())

# Split mapping
split_mapping = {
    'Training': 'train',
    'Validation': 'val',
    'Test': 'test'
}

# Her split için dosyaları organize et
for original_split, target_split in split_mapping.items():
    print(f"\n🔄 {original_split} -> {target_split} verisi işleniyor...")

    split_data = df[df['Split'] == original_split]
    copied_count = 0
    missing_count = 0

    for _, row in tqdm(split_data.iterrows(), total=len(split_data)):
        filename = row['Name']
        disease = row['Disease']

        # Kaynak dosya yolu
        src = os.path.join(image_folder, target_split, filename)

        # Hedef klasör ve dosya yolu
        dest_dir = os.path.join(output_base, target_split, disease)
        dest = os.path.join(dest_dir, filename)

        if not os.path.exists(src):
            missing_count += 1
            if missing_count <= 5:  # İlk 5 eksik dosyayı göster
                print(f"❌ Eksik görsel: {src}")
            continue

        os.makedirs(dest_dir, exist_ok=True)
        shutil.copy2(src, dest)
        copied_count += 1

    print(f"✅ {target_split}: {copied_count} dosya kopyalandı, {missing_count} dosya eksik")
