import os
import json
import shutil
from tqdm import tqdm

# 📍 Klasör yolları
base_path = "C:/Users/<USER>/OneDrive/Masaüstü/plant/plantsegv3"
image_folder = os.path.join(base_path, "images")
output_base = os.path.join(base_path, "classified")

print("📁 base_path:", base_path)
print("📁 image_folder:", image_folder)
print("📁 output_base (classified klasörü):", output_base)

splits = {
    "train": "annotation_train.json",
    "val": "annotation_val.json",
    "test": "annotation_test.json"
}

for split, json_file in splits.items():
    print(f"\n🔄 {split.upper()} verisi işleniyor...")

    with open(os.path.join(base_path, json_file), "r", encoding="utf-8") as f:
        data = json.load(f)

    id_to_filename = {img["id"]: img["file_name"] for img in data["images"]}
    id_to_label = {cat["id"]: cat["name"] for cat in data["categories"]}

    for ann in tqdm(data["annotations"]):
        img_id = ann["image_id"]
        cat_id = ann["category_id"]

        filename = id_to_filename.get(img_id)
        label = id_to_label.get(cat_id)

        if not filename or not label:
            continue

        src = os.path.join(image_folder, filename)
        dest_dir = os.path.join(output_base, split, label)
        dest = os.path.join(dest_dir, filename)

        if not os.path.exists(src):
            print(f"❌ Eksik görsel: {src}")
            continue

        os.makedirs(dest_dir, exist_ok=True)
        shutil.copy2(src, dest)
