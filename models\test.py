import numpy as np
import tensorflow as tf
from PIL import Image
import os

# Çalışma dizinini ayarla
os.chdir(r'C:\Users\<USER>\OneDrive\Masaüstü\plant')

# Load labels
with open('class.txt', 'r', encoding='utf-8') as f:
    labels = [line.strip() for line in f.readlines()]

# Load TFLite model - relatif yol kullan
interpreter = tf.lite.Interpreter(model_path='bitki_model.tflite')

interpreter.allocate_tensors()

# Get input and output details
input_details = interpreter.get_input_details()
output_details = interpreter.get_output_details()

print("Input dtype:", input_details[0]['dtype'])
print("Input shape:", input_details[0]['shape'])
print("Output dtype:", output_details[0]['dtype'])

# Function for preprocessing
def preprocess_image(image_path, input_shape):
    img = Image.open(image_path).convert("RGB")   # her zaman RGB yap
    img = img.resize((input_shape[1], input_shape[2]))
    img_array = np.array(img, dtype=np.float32) / 255.0  # Gradio ile aynı normalizasyon
    img_array = np.expand_dims(img_array, axis=0)
    return img_array.astype(input_details[0]['dtype'])

# Your image - relatif yol kullan
image_path = 'indir (12).jpeg'

# Preprocess the image
input_shape = input_details[0]['shape']
input_data = preprocess_image(image_path, input_shape)

# Run inference
interpreter.set_tensor(input_details[0]['index'], input_data)
interpreter.invoke()

# Get predictions
output = interpreter.get_tensor(output_details[0]['index'])[0]

# Get predicted class and confidence
predicted_index = np.argmax(output)
predicted_label = labels[predicted_index]
confidence = output[predicted_index]

# Print results
print(f'Predicted Class: {predicted_label}')
print(f'Confidence: {confidence:.4f}')