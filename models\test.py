import numpy as np
import tensorflow as tf
from PIL import Image

# Load labels
with open('C:/Users/<USER>/OneDrive/Masaüstü/plant/label.txt', 'r') as f:
    labels = [line.strip() for line in f.readlines()]

# Load TFLite model
interpreter = tf.lite.Interpreter(model_path='C:/Users/<USER>/OneDrive/Masaüstü/plant/hastalik_tanima_model.tflite')

interpreter.allocate_tensors()

# Get input and output details
input_details = interpreter.get_input_details()
output_details = interpreter.get_output_details()

# Function for preprocessing
def preprocess_image(image_path, input_shape):
    img = Image.open(image_path).resize((input_shape[1], input_shape[2]))
    img = np.array(img, dtype=np.float32) / 255.0  # adjust normalization as needed
    img = np.expand_dims(img, axis=0)
    return img.astype(input_details[0]['dtype'])

# Your image
image_path = 'C:/Users/<USER>/OneDrive/Masaüstü/plant/bell_pepper_blossom_end_rot_Bing_0053.jpg'

# Preprocess the image
input_shape = input_details[0]['shape']
input_data = preprocess_image(image_path, input_shape)

# Run inference
interpreter.set_tensor(input_details[0]['index'], input_data)
interpreter.invoke()

# Get predictions
output = interpreter.get_tensor(output_details[0]['index'])[0]

# Get predicted class and confidence
predicted_index = np.argmax(output)
predicted_label = labels[predicted_index]
confidence = output[predicted_index]

# Print results
print(f'Predicted Class: {predicted_label}')
print(f'Confidence: {confidence:.4f}')