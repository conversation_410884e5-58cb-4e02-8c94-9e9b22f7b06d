import os
import numpy as np
from PIL import Image

mask_dir = "C:/Users/<USER>/OneDrive/Masaüstü/plant/plantsegv3/annotations/train"

unique_colors = set()

for fname in os.listdir(mask_dir):
    if fname.endswith(".png"):
        path = os.path.join(mask_dir, fname)
        mask = Image.open(path).convert("L")  # gri tonlamalı oku
        arr = np.array(mask)
        unique_colors.update(np.unique(arr))

print("Maskelerdeki benzersiz değerler:", sorted(unique_colors))
print("Toplam sınıf sayısı:", len(unique_colors))
