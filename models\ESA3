import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.layers import GlobalAverage<PERSON>ooling2D, <PERSON><PERSON>, Multiply, Reshape , Concatenate
from tensorflow.keras.models import Model
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.layers import Conv2D, MultiHeadAttention
from tensorflow.keras.layers import LayerNormalization
import os
from PIL import Image, ImageFile

ImageFile.LOAD_TRUNCATED_IMAGES = True

def clean_broken_images(dataset_dir):
    for root, _, files in os.walk(dataset_dir):
        for file in files:
            if file.lower().endswith(('jpg','jpeg','png','bmp')):
                path = os.path.join(root, file)
                try:
                    img = Image.open(path)
                    img.verify()
                except Exception as e:
                    print(f"❌ Bozuk görsel silindi: {path}")
                    os.remove(path)

clean_broken_images("C:/Users/<USER>/OneDrive/Masaüstü/plant/Plant_Data/train")
clean_broken_images("C:/Users/<USER>/OneDrive/Masaüstü/plant/Plant_Data/valid")



IMAGE_SIZE = (128, 128)
BATCH_SIZE = 16
EPOCHS = 30
LR = 1e-4
NUM_CLASSES = 99

train_gen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=40,
    zoom_range=0.3,
    width_shift_range=0.3,
    height_shift_range=0.3,
    shear_range=0.3,
    brightness_range=[0.6,1.4],
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode="nearest"
).flow_from_directory(
    "C:/Users/<USER>/OneDrive/Masaüstü/plant/Plant_Data/train",  

    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode="categorical"
)

val_gen = ImageDataGenerator(rescale=1./255).flow_from_directory(
    "C:/Users/<USER>/OneDrive/Masaüstü/plant/Plant_Data/valid",
    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode="categorical"
)

def simple_attention_block(inputs):
    x = GlobalAveragePooling2D()(inputs)
    x = Dense(inputs.shape[-1] // 8, activation='relu')(x)
    x = Dense(inputs.shape[-1], activation='sigmoid')(x)
    x = Reshape((1, 1, inputs.shape[-1]))(x)  
    return Multiply()([inputs, x])

mobilenet = MobileNetV2(input_shape=IMAGE_SIZE + (3,), include_top=False, weights='imagenet')

mobilenet.trainable = True
fine_tune_at = 100 
for layer in mobilenet.layers[:fine_tune_at]:
    layer.trainable = False


mobilenet_output = GlobalAveragePooling2D()(mobilenet.output)
esa_features = simple_attention_block(mobilenet.output)
esa_out = GlobalAveragePooling2D()(esa_features)
combined = Concatenate()([mobilenet_output, esa_out])


x = Dense(256, activation='relu')(combined)
x = LayerNormalization()(x)
x = Dense(128, activation='relu')(x)
x = LayerNormalization()(x)
output = Dense(NUM_CLASSES, activation='softmax')(x)

model = Model(inputs=mobilenet.input, outputs=output)
model.compile(optimizer=Adam(learning_rate=LR), loss='categorical_crossentropy', metrics=['accuracy'])


model.fit(train_gen, validation_data=val_gen, epochs=EPOCHS)


model.save("bitki_best.keras")


converter = tf.lite.TFLiteConverter.from_keras_model(model)

converter.optimizations = [tf.lite.Optimize.DEFAULT]

tflite_model = converter.convert()

with open("bitki_model.tflite", "wb") as f:
    f.write(tflite_model)

print("✅ Optimize edilmiş TFLite modeli kaydedildi.")



