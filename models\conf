import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import json

# 📂 Doğrulama verisini yeniden yükle
IMAGE_SIZE = (128, 128)
BATCH_SIZE = 8
VAL_PATH = "C:/Users/<USER>/OneDrive/Masaüstü/plant/Plantsegv3/classified/val"

val_gen = ImageDataGenerator(rescale=1./255).flow_from_directory(
    VAL_PATH,
    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode="categorical",
    shuffle=False  # 🔴 shuffle=False → indexler doğru olsun
)

# 📦 Eğitilmiş modeli yükle
model = tf.keras.models.load_model("hastalik_tanima_best.keras")

# 🔍 Tahminleri al
y_pred = model.predict(val_gen, verbose=1)
y_pred_classes = np.argmax(y_pred, axis=1)
y_true = val_gen.classes

# 📄 Sınıf isimlerini al
class_names = list(val_gen.class_indices.keys())

# 📊 Confusion Matrix - Daha anlaşılabilir versiyonlar

# 1️⃣ Normalize edilmiş confusion matrix (yüzde olarak)
cm = confusion_matrix(y_true, y_pred_classes)
cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]

# 2️⃣ Büyük confusion matrix (tüm sınıflar)
plt.figure(figsize=(25, 20))
sns.heatmap(cm_normalized,
            annot=False,
            fmt='.2f',
            cmap='Blues',
            cbar_kws={'label': 'Doğruluk Oranı'},
            square=True)
plt.xlabel('Tahmin Edilen Sınıf', fontsize=14)
plt.ylabel('Gerçek Sınıf', fontsize=14)
plt.title('Normalize Confusion Matrix - Hastalık Tespiti\n(Değerler: Doğruluk Oranı)', fontsize=16)
plt.xticks(rotation=90, fontsize=8)
plt.yticks(rotation=0, fontsize=8)
plt.tight_layout()
plt.savefig("confusion_matrix_normalized.png", dpi=300, bbox_inches='tight')
plt.show()

# 3️⃣ En çok karıştırılan sınıfları göster
print("\n🔍 EN ÇOK KARIŞTIRILAN SINIFLAR:")
print("="*50)

# Diagonal dışındaki en yüksek değerleri bul
cm_copy = cm.copy()
np.fill_diagonal(cm_copy, 0)  # Diagonal'ı sıfırla

# En yüksek karışıklıkları bul
top_confusions = []
for i in range(len(class_names)):
    for j in range(len(class_names)):
        if i != j and cm_copy[i, j] > 0:
            top_confusions.append((cm_copy[i, j], class_names[i], class_names[j]))

# Sırala ve ilk 10'unu göster
top_confusions.sort(reverse=True)
for count, true_class, pred_class in top_confusions[:10]:
    print(f"{true_class} → {pred_class}: {count} kez karıştırıldı")

# 4️⃣ Sınıf başına doğruluk oranları
print("\n📊 SINIF BAŞINA DOĞRULUK ORANLARI:")
print("="*50)
class_accuracies = []
for i, class_name in enumerate(class_names):
    if cm[i].sum() > 0:
        accuracy = cm[i, i] / cm[i].sum()
        class_accuracies.append((accuracy, class_name, cm[i].sum()))
    else:
        class_accuracies.append((0, class_name, 0))

# Doğruluk oranına göre sırala
class_accuracies.sort(reverse=True)

print("En İyi Performans Gösteren Sınıflar:")
for acc, name, total in class_accuracies[:10]:
    print(f"{name}: {acc:.3f} ({total} örnek)")

print("\nEn Kötü Performans Gösteren Sınıflar:")
for acc, name, total in class_accuracies[-10:]:
    print(f"{name}: {acc:.3f} ({total} örnek)")

# 5️⃣ Genel istatistikler
total_correct = np.trace(cm)
total_samples = np.sum(cm)
overall_accuracy = total_correct / total_samples

print(f"\n📈 GENEL İSTATİSTİKLER:")
print("="*50)
print(f"Toplam Doğruluk: {overall_accuracy:.3f}")
print(f"Toplam Örnek Sayısı: {total_samples}")
print(f"Doğru Tahmin: {total_correct}")
print(f"Yanlış Tahmin: {total_samples - total_correct}")
print(f"Sınıf Sayısı: {len(class_names)}")

# 🧠 Detaylı sınıflandırma raporu
print("\n📋 DETAYLI SINIFLANDIRMA RAPORU:")
print("="*50)
print(classification_report(y_true, y_pred_classes, target_names=class_names))
