import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import json

# 📂 Doğrulama verisini yeniden yükle
IMAGE_SIZE = (128, 128)
BATCH_SIZE = 8
VAL_PATH = "C:/Users/<USER>/OneDrive/Masaüstü/plant/Plantsegv3/classified/val"

val_gen = ImageDataGenerator(rescale=1./255).flow_from_directory(
    VAL_PATH,
    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode="categorical",
    shuffle=False  # 🔴 shuffle=False → indexler doğru olsun
)

# 📦 Eğitilmiş modeli yükle
model = tf.keras.models.load_model("hastalik_tanima_best.keras")

# 🔍 Tahminleri al
y_pred = model.predict(val_gen, verbose=1)
y_pred_classes = np.argmax(y_pred, axis=1)
y_true = val_gen.classes

# 📄 Sınıf isimlerini al
class_names = list(val_gen.class_indices.keys())

# 📊 Confusion Matrix
cm = confusion_matrix(y_true, y_pred_classes)
plt.figure(figsize=(18, 18))
sns.heatmap(cm, annot=False, fmt='d', cmap='Blues', xticklabels=class_names, yticklabels=class_names)
plt.xlabel('Tahmin Edilen')
plt.ylabel('Gerçek')
plt.title('Confusion Matrix - Hastalık Tespiti')
plt.tight_layout()
plt.savefig("confusion_matrix.png")
plt.show()

# 🧠 Ekstra: Detaylı sınıflandırma raporu
print("\nSınıflandırma Raporu:\n")
print(classification_report(y_true, y_pred_classes, target_names=class_names))
