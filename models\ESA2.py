import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.layers import GlobalAveragePooling2D, Dense, Multiply, Reshape, Concatenate
from tensorflow.keras.models import Model
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.layers import LayerNormalization
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report


IMAGE_SIZE = (224, 224)
BATCH_SIZE = 16
EPOCHS = 15
LR = 1e-4
NUM_CLASSES = 114


train_gen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=30,
    zoom_range=0.2,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    horizontal_flip=True
).flow_from_directory(
    "C:/Users/<USER>/OneDrive/Masaüstü/plant/Plantsegv3/classified/train", 
    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode="categorical"
)

val_gen = ImageDataGenerator(rescale=1./255).flow_from_directory(
    "C:/Users/<USER>/OneDrive/Masaüstü/plant/Plantsegv3/classified/val",
    target_size=IMAGE_SIZE,
    batch_size=BATCH_SIZE,
    class_mode="categorical",
    shuffle=False 
)

def simple_attention_block(inputs):
    x = GlobalAveragePooling2D()(inputs)
    x = Dense(inputs.shape[-1] // 8, activation='relu')(x)
    x = Dense(inputs.shape[-1], activation='sigmoid')(x)
    x = Reshape((1, 1, inputs.shape[-1]))(x)  
    return Multiply()([inputs, x])


mobilenet = MobileNetV2(input_shape=IMAGE_SIZE + (3,), include_top=False, weights='imagenet')

mobilenet.trainable = True
fine_tune_at = 100 
for layer in mobilenet.layers[:fine_tune_at]:
    layer.trainable = False

mobilenet_output = GlobalAveragePooling2D()(mobilenet.output)
esa_features = simple_attention_block(mobilenet.output)
esa_out = GlobalAveragePooling2D()(esa_features)
combined = Concatenate()([mobilenet_output, esa_out])

x = Dense(256, activation='relu')(combined)
x = LayerNormalization()(x)
x = Dense(128, activation='relu')(x)
x = LayerNormalization()(x)
output = Dense(NUM_CLASSES, activation='softmax')(x)

model = Model(inputs=mobilenet.input, outputs=output)
model.compile(optimizer=Adam(learning_rate=LR), 
              loss='categorical_crossentropy', 
              metrics=['accuracy'])

model.fit(train_gen, validation_data=val_gen, epochs=EPOCHS)

model.save("hastalik_tanima_best.keras")

converter = tf.lite.TFLiteConverter.from_keras_model(model)
converter.optimizations = [tf.lite.Optimize.DEFAULT]
tflite_model = converter.convert()

with open("hastalik_tanima_model.tflite", "wb") as f:
    f.write(tflite_model)

print("✅ Model başarıyla mobil uyumlu .tflite formatına dönüştürüldü.")
